import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:kitemite_app/core/common/style/app_colors.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/ui/base_search_widget.dart';
import 'package:kitemite_app/core/common/widgets/image/base_cached_network_image.dart';
import 'package:kitemite_app/core/utils/build_context_extension.dart';
import 'package:kitemite_app/core/utils/date_time_formatter.dart';
import 'package:kitemite_app/features/map_store/provider/handle_navigator_provider/handle_navigator_provider.dart';
import 'package:kitemite_app/features/product_detail/ui/product_detail_screen.dart';
import 'package:kitemite_app/features/product_management/provider/product_management_provider.dart';
import 'package:kitemite_app/features/product_management/ui/sale_history/sale_history_tab.dart';
import 'package:kitemite_app/features/product_management/ui/widgets/skeleton/product_management_skeleton.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/generated/l10n.dart';
import 'package:kitemite_app/model/response/store/template_model.dart';
import 'package:kitemite_app/model/response/store/warehouse_model.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProductManagementArg {
  final int? initialTabIndex;
  final String? highlightProductId;
  final String? highlightOrderId;

  ProductManagementArg({
    this.initialTabIndex,
    this.highlightProductId,
    this.highlightOrderId,
  });
}

class ProductManagement extends ConsumerStatefulWidget {
  final ProductManagementArg? arg;
  const ProductManagement({
    super.key,
    this.arg,
  });

  @override
  ConsumerState<ProductManagement> createState() => _ProductManagementState();
}

class _ProductManagementState extends ConsumerState<ProductManagement>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _initializeWidget();
  }

  @override
  void didUpdateWidget(ProductManagement oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the arguments have changed

    // Delay the provider state modifications until after the build cycle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Clear any existing highlights
      ref.read(productManagementNotifierProvider.notifier).clearHighlight();
      // ref.read(productManagementNotifierProvider.notifier).refresh();
      // Handle new arguments
      _handleNewArguments();
    });
  }

  void _initializeWidget() {
    // Set initial tab index (default to 0 if not specified, or 2 for history if highlightProductId is provided)
    final initialIndex = widget.arg?.initialTabIndex ??
        (widget.arg?.highlightProductId != null ? 2 : 0);
    _tabController =
        TabController(length: 3, vsync: this, initialIndex: initialIndex);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(productManagementNotifierProvider.notifier).fetchInitialData();
      _handleNewArguments();
    });

    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        ref
            .read(productManagementNotifierProvider.notifier)
            .setSelectedTabIndex(_tabController.index);
        setState(() {}); // Trigger rebuild to update search widget visibility
      }
    });
  }

  void _handleNewArguments() {
    // If we need to highlight a product, set it in the provider
    if (widget.arg?.highlightProductId != null) {
      ref
          .read(productManagementNotifierProvider.notifier)
          .setHighlightProductIdWithDate(
              widget.arg!.highlightProductId!, widget.arg!.highlightOrderId!);

      // Set the selected tab index to history tab (index 2) in the provider
      ref
          .read(productManagementNotifierProvider.notifier)
          .setSelectedTabIndex(2);

      // Update tab controller if necessary
      if (_tabController.index != 2) {
        _tabController.animateTo(2);
      }
    }
  }

  Widget _buildSkeletonLoading() {
    return Expanded(
      child: Skeletonizer(
        enabled: true,
        child: TabBarView(
          controller: _tabController,
          physics:
              const NeverScrollableScrollPhysics(), // Disable swipe gesture
          children: [
            ProductManagementSkeleton.buildProductsSkeleton(),
            ProductManagementSkeleton.buildTemplatesSkeleton(),
            ProductManagementSkeleton.buildHistorySkeleton(),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(productManagementNotifierProvider);
    // Show snackbars for template deletion status
    ref.listen(productManagementNotifierProvider, (previous, current) {
      if (current.deleteTemplateError != null &&
          current.deleteTemplateError != previous?.deleteTemplateError) {
        context.showErrorSnackBar(current.deleteTemplateError!);
      }
      if (current.deleteTemplateSuccess != null &&
          current.deleteTemplateSuccess != previous?.deleteTemplateSuccess) {
        context.showSuccessSnackBar(current.deleteTemplateSuccess!);
      }
    });

    return SafeArea(
        top: false,
        maintainBottomViewPadding: false,
        child: Column(children: [
          SizedBox(height: 8.h),
          Text(S.current.productManagementYourProducts,
              style: AppTextStyles.bold(20.sp, color: AppColors.textPrimary)),
          SizedBox(height: 16.h),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(30),
            ),
            // Thêm clipBehavior để cắt animation overflow
            clipBehavior: Clip.antiAlias,
            child: TabBar(
              controller: _tabController,
              labelColor: AppColors.textPrimary,
              unselectedLabelColor: Colors.black,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              labelStyle:
                  AppTextStyles.bold(14.sp, color: AppColors.textPrimary),
              unselectedLabelStyle:
                  AppTextStyles.bold(14.sp, color: AppColors.textPrimary),
              indicator: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(30),
              ),
              tabs: [
                Tab(text: S.current.productManagementProducts),
                Tab(text: S.current.productManagementTemplate),
                Tab(text: S.current.productManagementHistory),
              ],
            ),
          ),
          if (_tabController.index !=
              2) // Hide search widget when History tab is selected (index 2)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: BaseSearchWidget(
                onSearch: (value) {
                  ref
                      .read(productManagementNotifierProvider.notifier)
                      .setSearchQuery(value);
                },
              ),
            ),
          if (state.isLoading)
            _buildSkeletonLoading()
          else
            Expanded(
              child: TabBarView(
                controller: _tabController,
                // physics:
                //     const NeverScrollableScrollPhysics(), // Disable swipe gesture
                children: [
                  // Products Tab
                  _buildProductsTab(),
                  // Template Tab
                  _buildTemplatesTab(),
                  // History Tab
                  _buildHistoryTab(),
                ],
              ),
            ),
        ]));
  }

  Widget _buildProductsTab() {
    final filteredWarehouses = ref
        .read(productManagementNotifierProvider.notifier)
        .getFilteredWarehouses();

    if (filteredWarehouses.isEmpty) {
      return RefreshIndicator(
        onRefresh: () =>
            ref.read(productManagementNotifierProvider.notifier).refresh(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.iconEmptyProduct.image(),
              SizedBox(height: 16.h),
              Text(
                S.current.productManagementNoProducts,
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () =>
          ref.read(productManagementNotifierProvider.notifier).refresh(),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: filteredWarehouses.length,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) =>
            buildProductItem(filteredWarehouses[index]),
      ),
    );
  }

  Widget _buildTemplatesTab() {
    final filteredTemplates = ref
        .read(productManagementNotifierProvider.notifier)
        .getFilteredTemplates();

    if (filteredTemplates.isEmpty) {
      return RefreshIndicator(
        onRefresh: () =>
            ref.read(productManagementNotifierProvider.notifier).refresh(),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.iconEmptyProduct.image(),
              SizedBox(height: 16.h),
              Text(
                S.current.productManagementNoTemplates,
                style: AppTextStyles.regular(14.sp,
                    color: AppColors.textLightSecondary),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () =>
          ref.read(productManagementNotifierProvider.notifier).refresh(),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: filteredTemplates.length,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) =>
            buildTemplate(filteredTemplates[index]),
      ),
    );
  }

  Widget _buildHistoryTab() {
    return const SaleHistoryTab();
  }

  Widget buildTemplate(TemplateModel template) {
    return Slidable(
      endActionPane: ActionPane(
        extentRatio: 0.15,
        motion: const ScrollMotion(),
        children: [
          CustomSlidableAction(
            onPressed: (context) {
              ref
                  .read(productManagementNotifierProvider.notifier)
                  .deleteTemplate(template.id!);
            },
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.all(4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.delete,
                  color: Colors.white,
                ),
                SizedBox(height: 2.h),
                Text(
                  "消去",
                  style: AppTextStyles.regular(12.sp, color: Colors.white),
                ),
              ],
            ),
          )
        ],
      ),
      child: GestureDetector(
        onTap: () {
          context
              .push(RouterPaths.detailTemplate, extra: template.id)
              .then((value) {
            ref.read(productManagementNotifierProvider.notifier).refresh();
          });
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: BaseCachedNetworkImage(
                  imageUrl: template.baseImg ?? '',
                  width: 84,
                  height: 84,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      template.name ?? '',
                      style: AppTextStyles.bold(14.sp,
                          color: AppColors.textPrimary),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildProductItem(WarehouseModel warehouseModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  warehouseModel.name ?? '',
                  style:
                      AppTextStyles.bold(16.sp, color: AppColors.textPrimary),
                ),
                Text(
                  "${warehouseModel.province ?? ""}, ${warehouseModel.city ?? ""}, ${warehouseModel.street ?? ""}",
                  style: AppTextStyles.regular(12.sp,
                      color: AppColors.textLightSecondary),
                ),
              ],
            )),
        ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: warehouseModel.products.length,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              final product = warehouseModel.products[index];
              return GestureDetector(
                onTap: () {
                  ref
                      .read(handleNavigatorNotifierProvider.notifier)
                      .setPopToRouteUpdate(RouterPaths.business);
                  context.push(RouterPaths.productDetail,
                      extra: ProductDetailScreenArg(
                          productId: product.id ?? 0,
                          isUpdateProduct: true,
                          isProductActive: true,
                          isLoginWithAccount: true));
                },
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: BaseCachedNetworkImage(
                          imageUrl: product.img ?? '',
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(product.name ?? '',
                                style: AppTextStyles.bold(14.sp,
                                    color: AppColors.textPrimary)),
                            SizedBox(height: 8.h),
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                      "有効期限: ${DateTimeFormatter.formatDateFromApi(product.expirationDate ?? "")}",
                                      style: AppTextStyles.medium(12.sp,
                                          color: AppColors.textLightSecondary)),
                                ),
                                Text(
                                    "Added: ${DateTimeFormatter.formatDateFromApi(product.stockHistory?.date ?? "")}",
                                    style: AppTextStyles.medium(12.sp,
                                        color: AppColors.textLightSecondary)),
                              ],
                            ),
                            Row(
                              children: [
                                Chip(
                                  label: Text(
                                    "冷凍庫-${product.cabinetCode ?? ''}",
                                    style: AppTextStyles.medium(12.sp,
                                        color: AppColors.textLightSecondary),
                                  ),
                                  backgroundColor: AppColors.grey200,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                      side: const BorderSide(
                                          color: AppColors.grey200, width: 1)),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                ),
                                const SizedBox(width: 8),
                                Chip(
                                  label: Text(
                                    "No.${product.shelf?.shelfCode ?? ''}",
                                    style: AppTextStyles.medium(12.sp,
                                        color: AppColors.textLightSecondary),
                                  ),
                                  backgroundColor: AppColors.grey200,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                      side: const BorderSide(
                                          color: AppColors.grey200, width: 1)),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }),
        SizedBox(height: 16.h),
      ],
    );
  }
}
