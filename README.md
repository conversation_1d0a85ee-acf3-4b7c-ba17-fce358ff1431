# kitemite_app

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

//build runner
dart run build_runner build --delete-conflicting-outputs
flutter build apk --flavor dev -t lib/main_dev.dart
flutter build apk --flavor prod -t lib/main_prod.dart

flutter build appbundle --flavor prod -t lib/main_prod.dart
flutter build ios --release --flavor prod --target lib/main_prod.dart


dart run intl_utils:generate

// deloy firebase distribution
chmod +x upload_firebase.sh

./upload_firebase.sh
